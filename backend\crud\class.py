# crud/class.py
from typing import Optional, List
from sqlalchemy.orm import Session
from crud.base import CRUDBase
from models.class import Class
from schemas.class import ClassCreate, ClassUpdate

class CRUDClass(CRUDBase[Class, ClassCreate, ClassUpdate]):
    def get_by_name_and_year(
        self, db: Session, *, nom: str, annee_scolaire_id: int
    ) -> Optional[Class]:
        return db.query(Class).filter(
            Class.nom == nom,
            Class.annee_scolaire_id == annee_scolaire_id
        ).first()
    
    def get_by_school_year(self, db: Session, *, annee_scolaire_id: int) -> List[Class]:
        return db.query(Class).filter(Class.annee_scolaire_id == annee_scolaire_id).all()
    
    def get_by_niveau(self, db: Session, *, niveau: str) -> List[Class]:
        return db.query(Class).filter(Class.niveau == niveau).all()

class_crud = CRUDClass(Class)
