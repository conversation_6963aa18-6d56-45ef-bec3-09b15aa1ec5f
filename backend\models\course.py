# models/course.py
from sqlalchemy import Column, Integer, String, ForeignKey, DateTime, Text
from sqlalchemy.orm import relationship
from datetime import datetime
from db import Base

class Course(Base):
    __tablename__ = "courses"

    id = Column(Integer, primary_key=True, index=True)
    enseignant_id = Column(Integer, ForeignKey("teachers.id"), nullable=False)
    matiere_id = Column(Integer, ForeignKey("subjects.id"), nullable=False)
    classe_id = Column(Integer, ForeignKey("classes.id"), nullable=False)
    horaire = Column(Text, nullable=True)  # Schedule information (JSON or text)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    teacher = relationship("Teacher", back_populates="courses")
    subject = relationship("Subject", back_populates="courses")
    classe = relationship("Class", back_populates="courses")
    grades = relationship("Grade", back_populates="course")
    attendances = relationship("Attendance", back_populates="course")
