# routes/classes.py
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from utils.security import get_current_active_user, get_db
from crud.class import class_crud
from schemas.class import ClassCreate, ClassRead, ClassUpdate
from services.academic_service import AcademicService

router = APIRouter(prefix="/api/classes", tags=["Classes"])

@router.get("/", response_model=List[ClassRead])
async def get_classes(
    skip: int = 0,
    limit: int = 100,
    school_year_id: int = Query(None, description="Filter by school year"),
    niveau: str = Query(None, description="Filter by level"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get all classes with optional filters"""
    if school_year_id:
        return class_crud.get_by_school_year(db, annee_scolaire_id=school_year_id)
    elif niveau:
        return class_crud.get_by_niveau(db, niveau=niveau)
    else:
        return class_crud.get_multi(db, skip=skip, limit=limit)

@router.get("/{class_id}", response_model=ClassRead)
async def get_class(
    class_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get a specific class by ID"""
    db_class = class_crud.get(db, class_id)
    if not db_class:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Class not found"
        )
    return db_class

@router.get("/{class_id}/ranking")
async def get_class_ranking(
    class_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get student ranking for a class"""
    db_class = class_crud.get(db, class_id)
    if not db_class:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Class not found"
        )
    
    ranking = AcademicService.get_class_ranking(db, class_id)
    return ranking

@router.get("/{class_id}/attendance-report")
async def get_class_attendance_report(
    class_id: int,
    start_date: str = Query(None, description="Start date (YYYY-MM-DD)"),
    end_date: str = Query(None, description="End date (YYYY-MM-DD)"),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Get attendance report for a class"""
    db_class = class_crud.get(db, class_id)
    if not db_class:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Class not found"
        )
    
    from datetime import datetime
    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date() if start_date else None
    end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date() if end_date else None
    
    report = AcademicService.get_attendance_report(
        db, class_id=class_id, start_date=start_date_obj, end_date=end_date_obj
    )
    return report

@router.post("/", response_model=ClassRead)
async def create_class(
    class_in: ClassCreate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Create a new class"""
    # Check if class already exists for this school year
    existing = class_crud.get_by_name_and_year(
        db, nom=class_in.nom, annee_scolaire_id=class_in.annee_scolaire_id
    )
    if existing:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Class with this name already exists for this school year"
        )
    
    return class_crud.create(db, obj_in=class_in)

@router.put("/{class_id}", response_model=ClassRead)
async def update_class(
    class_id: int,
    class_in: ClassUpdate,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Update a class"""
    db_class = class_crud.get(db, class_id)
    if not db_class:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Class not found"
        )
    
    return class_crud.update(db, db_obj=db_class, obj_in=class_in)

@router.delete("/{class_id}")
async def delete_class(
    class_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_active_user)
):
    """Delete a class"""
    db_class = class_crud.get(db, class_id)
    if not db_class:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Class not found"
        )
    
    # Check if class has students
    if db_class.effectif > 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete class with enrolled students"
        )
    
    class_crud.remove(db, id=class_id)
    return {"message": "Class deleted successfully"}
