import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  Menu,
  X,
  Search,
  Bell,
  User,
  LogOut,
  Home,
  Users,
  FileText,
  BookOpen,
  BarChart3,
  List,
  CreditCard,
  PieChart,
  UserCheck,
  Briefcase,
  Calendar,
  MessageSquare,
  FileEdit,
  Database
} from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useNotifications } from '../contexts/NotificationContext';
import logo from '../images/logo.png';

const navigationItems = [
  { name: 'Tableau de bord', href: '/dashboard', icon: Home, roles: ['admin', 'secretary'] },
  { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/eleves', icon: Users, roles: ['admin', 'secretary'] },
  { name: 'Certificats', href: '/certificats', icon: FileText, roles: ['admin', 'secretary'] },
  { name: 'Bulletins', href: '/bulletins', icon: BookO<PERSON>, roles: ['admin', 'secretary'] },
  { name: 'Évaluations', href: '/evaluations', icon: BarChart3, roles: ['admin'] },
  { name: 'Statistiques', href: '/statistiques', icon: PieChart, roles: ['admin'] },
  { name: 'Liste nominative', href: '/liste-eleves', icon: List, roles: ['admin', 'secretary'] },
  { name: 'Paiements', href: '/paiements', icon: CreditCard, roles: ['admin', 'secretary'] },
  { name: 'Finances', href: '/finances', icon: PieChart, roles: ['admin'] },
  { name: 'Professeurs', href: '/professeurs', icon: UserCheck, roles: ['admin', 'secretary'] },
  { name: 'Fiches de paie', href: '/fiches-paie', icon: Briefcase, roles: ['admin'] },
  { name: 'Absences', href: '/absences', icon: Calendar, roles: ['admin', 'secretary'] },
  { name: 'Notifications', href: '/notifications', icon: MessageSquare, roles: ['admin', 'secretary'] },
  { name: 'Formulaires', href: '/formulaires', icon: FileEdit, roles: ['admin'] },
  { name: 'Sauvegarde', href: '/sauvegarde', icon: Database, roles: ['admin'] },
];

export default function Layout({ children }: { children: React.ReactNode }) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const { user, logout } = useAuth();
  const { unreadCount } = useNotifications();
  const location = useLocation();

  const filteredNavigation = navigationItems.filter(item =>
    item.roles.includes(user?.role as string)
  );

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar mobile */}
      {sidebarOpen && (
        <div className="fixed inset-0 z-50 lg:hidden">
          <div className="fixed inset-0 bg-gray-600 bg-opacity-75" onClick={() => setSidebarOpen(false)} />
          <div className="relative flex w-full max-w-xs flex-col bg-white shadow-xl">
            <div className="flex h-16 items-center justify-between px-4" style={{ backgroundColor: '#0a1186' }}>
              <div className="flex items-center">
                {/*<GraduationCap className="h-8 w-8 text-white" /> */}
                <img
                  src={logo}
                  alt="Logo de l'école"
                  className="h-8 w-auto"
                />
                <span className="ml-2 text-xl font-bold text-white">École AI</span>
              </div>
              <button onClick={() => setSidebarOpen(false)} className="text-white">
                <X className="h-6 w-6" />
              </button>
            </div>
            <nav className="flex-1 px-4 py-6 space-y-1">
              {filteredNavigation.map((item) => {
                const isActive = location.pathname === item.href;
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${isActive
                      ? 'text-white shadow-lg'
                      : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    style={isActive ? { backgroundColor: '#0a1186' } : {}}
                    onClick={() => setSidebarOpen(false)}
                  >
                    <item.icon className={`mr-3 h-5 w-5 ${isActive ? 'text-white' : 'text-gray-500'}`} />
                    {item.name}
                  </Link>
                );
              })}
            </nav>
          </div>
        </div>
      )}

      {/* Sidebar desktop */}
      <div className="hidden lg:flex lg:flex-shrink-0">
        <div className="flex w-64 flex-col fixed left-0 top-0 h-full z-30">
          <div className="flex min-h-0 flex-1 flex-col bg-white shadow-lg">
            <div className="flex h-16 items-center px-4 flex-shrink-0" style={{ backgroundColor: '#0a1186' }}>
              {/*<GraduationCap className="h-8 w-8 text-white" /> */}
              <img
                src={logo}
                alt="Logo de l'école"
                className="h-8 w-auto"
              />
              <span className="ml-2 text-xl font-bold text-white">École AI</span>
            </div>
            <nav className="flex-1 px-4 py-6 space-y-1 overflow-y-auto overflow-x-hidden">
              {filteredNavigation.map((item) => {
                const isActive = location.pathname === item.href;
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`group flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${isActive
                      ? 'text-white shadow-lg'
                      : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    style={isActive ? { backgroundColor: '#0a1186' } : {}}
                  >
                    <item.icon className={`mr-3 h-5 w-5 ${isActive ? 'text-white' : 'text-gray-500'}`} />
                    {item.name}
                  </Link>
                );
              })}
            </nav>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex min-w-0 flex-1 flex-col lg:ml-64">
        {/* Header */}
        <div className="sticky top-0 z-20 flex h-16 flex-shrink-0 items-center bg-white border-b border-gray-200 px-4">
          <button
            onClick={() => setSidebarOpen(true)}
            className="lg:hidden -ml-2 mr-3 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none"
          >
            <Menu className="h-6 w-6" />
          </button>

          {/* Search bar */}
          <div className="flex-1 max-w-2xl mx-4">
            <div className="relative">
              <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Rechercher un élève, professeur, paiement..."
                className="block w-full rounded-lg border border-gray-300 bg-gray-50 pl-10 pr-3 py-2 text-sm placeholder-gray-500 focus:border-blue-500 focus:ring-1 focus:ring-blue-500 focus:bg-white transition-colors"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>

          {/* User menu */}
          <div className="flex items-center space-x-4">
            <Link
              to="/notifications"
              className="relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full"
            >
              <Bell className="h-6 w-6" />
              {unreadCount > 0 && (
                <span
                  className="absolute -top-1 -right-1 h-5 w-5 rounded-full text-xs font-medium text-white flex items-center justify-center"
                  style={{ backgroundColor: '#ffdd5a', color: '#0a1186' }}
                >
                  {unreadCount}
                </span>
              )}
            </Link>

            <div className="flex items-center space-x-2">
              <div className="hidden sm:block text-right">
                <p className="text-sm font-medium text-gray-900">{user?.full_name}</p>
                <p className="text-xs text-gray-500 capitalize">{user?.role === 'admin' ? 'Administrateur' : 'Secrétaire'}</p>
              </div>
              <div className="h-8 w-8 rounded-full flex items-center justify-center text-white" style={{ backgroundColor: '#0a1186' }}>
                <User className="h-4 w-4" />
              </div>
              <button
                onClick={logout}
                className="p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-full"
                title="Se déconnecter"
              >
                <LogOut className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
      </div>

      {/* Development tools */}
      {/* <AuthTester /> */}
    </div>
  );
}