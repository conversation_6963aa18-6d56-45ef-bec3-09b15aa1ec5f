import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { SecureTokenStorage } from '../utils/tokenStorage';
import { envConfig } from '../config/environment';
import { User } from '../services/authService';

export interface SessionInfo {
    user: User | null;
    isAuthenticated: boolean;
    sessionExpiry: Date | null;
    timeUntilExpiry: number | null;
    isExpiringSoon: boolean;
    sessionDuration: number | null;
}

export function useSession(): SessionInfo {
    const { user, isAuthenticated } = useAuth();
    const [sessionExpiry, setSessionExpiry] = useState<Date | null>(null);
    const [sessionStart, setSessionStart] = useState<Date | null>(null);

    useEffect(() => {
        if (isAuthenticated) {
            const token = SecureTokenStorage.getAccessToken();
            if (token) {
                try {
                    const expiration = SecureTokenStorage.getTokenExpiration(token);
                    setSessionExpiry(expiration);

                    if (!sessionStart) {
                        setSessionStart(new Date());
                    }
                } catch (error) {
                    console.error('Error parsing token:', error);
                    setSessionExpiry(null);
                }
            }
        } else {
            setSessionExpiry(null);
            setSessionStart(null);
        }
    }, [isAuthenticated, sessionStart]);

    const timeUntilExpiry = sessionExpiry ? sessionExpiry.getTime() - Date.now() : null;
    const isExpiringSoon = timeUntilExpiry ? timeUntilExpiry < envConfig.tokenRefreshThreshold : false;
    const sessionDuration = sessionStart ? Date.now() - sessionStart.getTime() : null;

    return {
        user,
        isAuthenticated,
        sessionExpiry,
        timeUntilExpiry,
        isExpiringSoon,
        sessionDuration
    };
}