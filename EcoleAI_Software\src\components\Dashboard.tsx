import { Users, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, TrendingUp, Calendar, BookOpen, Target } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

const StatCard = ({ title, value, icon: Icon, color, trend, trendValue }: {
  title: string;
  value: string;
  icon: any;
  color: string;
  trend?: 'up' | 'down';
  trendValue?: string;
}) => (
  <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm font-medium text-gray-600">{title}</p>
        <p className="text-3xl font-bold text-gray-900 mt-2">{value}</p>
        {trend && trendValue && (
          <div className={`flex items-center mt-2 text-sm ${trend === 'up' ? 'text-green-600' : 'text-red-600'}`}>
            <TrendingUp className={`h-4 w-4 mr-1 ${trend === 'down' ? 'rotate-180' : ''}`} />
            <span>{trendValue}</span>
          </div>
        )}
      </div>
      <div className="h-12 w-12 rounded-lg flex items-center justify-center" style={{ backgroundColor: color }}>
        <Icon className="h-6 w-6 text-white" />
      </div>
    </div>
  </div>
);

export default function Dashboard() {
  const { user } = useAuth();
  const isAdmin = user?.role === 'admin';

  const adminStats = [
    {
      title: 'Total Élèves',
      value: '847',
      icon: Users,
      color: '#0a1186',
      trend: 'up' as const,
      trendValue: '+12 ce mois'
    },
    {
      title: 'Revenus Mensuels',
      value: '2.4M AR',
      icon: CreditCard,
      color: '#ffdd5a',
      trend: 'up' as const,
      trendValue: '+8.2%'
    },
    {
      title: 'Professeurs',
      value: '34',
      icon: UserCheck,
      color: '#a1ecff',
      trend: 'up' as const,
      trendValue: '+2 nouveaux'
    },
    {
      title: 'Alertes Critiques',
      value: '5',
      icon: AlertTriangle,
      color: '#FF6B6B',
      trend: 'down' as const,
      trendValue: '-3 résolues'
    }
  ];

  const secretaryStats = [
    {
      title: 'Nouvelles Inscriptions',
      value: '23',
      icon: Users,
      color: '#0a1186',
      trend: 'up' as const,
      trendValue: 'Cette semaine'
    },
    {
      title: 'Absences du Jour',
      value: '12',
      icon: Calendar,
      color: '#ffdd5a'
    },
    {
      title: 'Paiements Reçus',
      value: '340K AR',
      icon: CreditCard,
      color: '#a1ecff',
      trend: 'up' as const,
      trendValue: 'Aujourd\'hui'
    },
    {
      title: 'Certificats Émis',
      value: '8',
      icon: BookOpen,
      color: '#e3ea9c',
      trend: 'up' as const,
      trendValue: 'Cette semaine'
    }
  ];

  const recentActivities = isAdmin ? [
    { action: 'Nouveau bulletin généré', details: 'Classe 6ème A - 1er trimestre', time: 'Il y a 2h' },
    { action: 'Inscription validée', details: 'RAVELOJAONA Marie - CM2 B', time: 'Il y a 3h' },
    { action: 'Paiement reçu', details: 'Frais de scolarité - 45,000 AR', time: 'Il y a 4h' },
    { action: 'Absence signalée', details: 'RAKOTO Paul - 5ème C', time: 'Il y a 5h' }
  ] : [
    { action: 'Certificat de scolarité émis', details: 'RASOLONDRAIBE Solo - 6ème A', time: 'Il y a 1h' },
    { action: 'Inscription mise à jour', details: 'RAMANANDRAIBE Vonjy - CM1 B', time: 'Il y a 2h' },
    { action: 'Paiement enregistré', details: 'Uniforme scolaire - 15,000 AR', time: 'Il y a 3h' },
    { action: 'Absence enregistrée', details: 'RAVELOSON Toky - CE2 A', time: 'Il y a 4h' }
  ];

  const stats = isAdmin ? adminStats : secretaryStats;

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Bonjour, {user?.full_name}
          </h1>
          <p className="text-gray-600 mt-1">
            Voici un aperçu de votre tableau de bord
          </p>
        </div>
        <div className="mt-4 sm:mt-0 text-right">
          <p className="text-sm text-gray-500">Dernière connexion</p>
          <p className="font-medium text-gray-900">
            {new Date().toLocaleDateString('fr-FR', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </p>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <StatCard key={index} {...stat} />
        ))}
      </div>

      {/* Charts and Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions Rapides</h3>
          <div className="grid grid-cols-2 gap-4">
            <button className="p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors text-left">
              <Target className="h-8 w-8 mb-2" style={{ color: '#0a1186' }} />
              <p className="font-medium text-gray-900">Nouvelle Inscription</p>
              <p className="text-sm text-gray-500">Ajouter un élève</p>
            </button>
            <button className="p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors text-left">
              <BookOpen className="h-8 w-8 mb-2" style={{ color: '#ffdd5a' }} />
              <p className="font-medium text-gray-900">Certificat</p>
              <p className="text-sm text-gray-500">Générer un document</p>
            </button>
            <button className="p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors text-left">
              <CreditCard className="h-8 w-8 mb-2 text-green-600" />
              <p className="font-medium text-gray-900">Paiement</p>
              <p className="text-sm text-gray-500">Enregistrer un versement</p>
            </button>
            <button className="p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors text-left">
              <Calendar className="h-8 w-8 mb-2 text-purple-600" />
              <p className="font-medium text-gray-900">Absence</p>
              <p className="text-sm text-gray-500">Marquer une absence</p>
            </button>
          </div>
        </div>

        {/* Recent Activities */}
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Activités Récentes</h3>
          <div className="space-y-4">
            {recentActivities.map((activity, index) => (
              <div key={index} className="flex items-start space-x-3">
                <div className="h-2 w-2 rounded-full mt-2" style={{ backgroundColor: '#0a1186' }}></div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                  <p className="text-sm text-gray-500">{activity.details}</p>
                  <p className="text-xs text-gray-400 mt-1">{activity.time}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Monthly Overview */}
      {isAdmin && (
        <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Aperçu Mensuel</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="h-16 w-16 mx-auto rounded-full flex items-center justify-center mb-3" style={{ backgroundColor: '#e3ea9c' }}>
                <TrendingUp className="h-8 w-8 text-green-700" />
              </div>
              <p className="text-2xl font-bold text-gray-900">94.2%</p>
              <p className="text-sm text-gray-600">Taux de présence</p>
            </div>
            <div className="text-center">
              <div className="h-16 w-16 mx-auto rounded-full flex items-center justify-center mb-3" style={{ backgroundColor: '#a1ecff' }}>
                <Target className="h-8 w-8" style={{ color: '#0a1186' }} />
              </div>
              <p className="text-2xl font-bold text-gray-900">87.5%</p>
              <p className="text-sm text-gray-600">Taux de réussite</p>
            </div>
            <div className="text-center">
              <div className="h-16 w-16 mx-auto rounded-full flex items-center justify-center mb-3" style={{ backgroundColor: '#ffdd5a' }}>
                <CreditCard className="h-8 w-8" style={{ color: '#0a1186' }} />
              </div>
              <p className="text-2xl font-bold text-gray-900">89.1%</p>
              <p className="text-sm text-gray-600">Paiements à jour</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}