# models/student.py
from sqlalchemy import Column, Integer, String, Date, ForeignKey, DateTime, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime, date
from db import Base

class Student(Base):
    __tablename__ = "students"

    id = Column(Integer, primary_key=True, index=True)
    matricule = Column(String, unique=True, nullable=False, index=True)  # Unique student ID
    nom = Column(String, nullable=False, index=True)
    prenom = Column(String, nullable=False, index=True)
    date_naissance = Column(Date, nullable=False)
    sexe = Column(String, nullable=False)  # M/F
    adresse = Column(String, nullable=True)
    nom_parent = Column(String, nullable=False)
    contact_parent = Column(String, nullable=False)
    classe_id = Column(Integer, ForeignKey("classes.id"), nullable=False)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

    # Relationships
    classe = relationship("Class", back_populates="students")
    grades = relationship("Grade", back_populates="student")
    attendances = relationship("Attendance", back_populates="student")
    payments = relationship("Payment", back_populates="student")
