# models/school_year.py
from sqlalchemy import Column, Integer, String, Date, Boolean
from datetime import datetime, date
from db import Base
from sqlalchemy.orm import relationship

class SchoolYear(Base):
    __tablename__ = "school_years"

    id = Column(Integer, primary_key=True, index=True)
    annee = Column(String, unique=True, nullable=False, index=True)  # e.g., "2024-2025"
    date_debut = Column(Date, nullable=False)
    date_fin = Column(Date, nullable=False)
    is_active = Column(Boolean, default=False)  # Only one active year at a time
    created_at = Column(Date, default=date.today)

    # Relationships
    classes = relationship("Class", back_populates="school_year")
